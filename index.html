<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱生成器 - 快速生成临时邮箱</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-envelope"></i> 邮箱生成器</h1>
            <p class="subtitle">快速生成临时邮箱地址</p>
        </header>

        <main class="main-content">
            <div class="generator-card">
                <div class="input-section">
                    <h2>生成设置</h2>
                    
                    <div class="form-group">
                        <label for="emailPrefix">邮箱前缀 (可选)</label>
                        <input type="text" id="emailPrefix" placeholder="留空自动生成 (5位数字+3位字母)" maxlength="8">
                        <small class="help-text">格式：5位数字 + 3位小写字母，如：12345abc</small>
                    </div>

                    <div class="form-group">
                        <label for="emailDomain">邮箱域名</label>
                        <select id="emailDomain">
                            <option value="@ai999.dpdns.org">@ai999.dpdns.org</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="emailCount">生成数量</label>
                        <input type="number" id="emailCount" min="1" max="10" value="1">
                    </div>

                    <button id="generateBtn" class="generate-btn">
                        <i class="fas fa-magic"></i> 生成邮箱
                    </button>
                </div>

                <div class="results-section" id="resultsSection" style="display: none;">
                    <h2>生成结果</h2>
                    <div id="emailResults" class="email-results"></div>
                    <button id="copyAllBtn" class="copy-all-btn">
                        <i class="fas fa-copy"></i> 复制所有
                    </button>
                </div>
            </div>

            <div class="status-section">
                <div id="loadingIndicator" class="loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 正在生成邮箱...
                </div>
                <div id="statusMessage" class="status-message"></div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 邮箱生成器. 所有权利保留.</p>
            <p class="api-info">API 域名: ai999.dpdns.org</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
