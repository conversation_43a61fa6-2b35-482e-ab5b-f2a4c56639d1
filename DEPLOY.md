# Cloudflare Pages 部署指南

## 快速部署步骤

### 方法一：通过GitHub仓库部署（推荐）

1. **准备Git仓库**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: 邮箱生成器项目"
   git branch -M main
   git remote add origin https://github.com/yourusername/xinmail-generator.git
   git push -u origin main
   ```

2. **Cloudflare Pages设置**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 "Pages" 页面
   - 点击 "创建项目" → "连接到Git"
   - 选择GitHub仓库：`xinmail-generator`
   - 配置构建设置：
     - **项目名称**: `xinmail-generator`
     - **生产分支**: `main`
     - **构建命令**: 留空
     - **构建输出目录**: `/`
   - 点击 "保存并部署"

3. **自定义域名配置**
   - 部署完成后，进入项目设置
   - 点击 "自定义域" → "设置自定义域"
   - 添加域名：`eoceshi.552500.xyz`
   - 按照提示配置DNS记录

### 方法二：直接文件上传

1. **打包项目文件**
   - 选择所有项目文件（除了.git文件夹）
   - 创建ZIP压缩包

2. **上传到Cloudflare Pages**
   - 登录 Cloudflare Dashboard
   - 进入 "Pages" 页面
   - 点击 "上传资产"
   - 上传ZIP文件或拖拽文件夹
   - 设置项目名称：`xinmail-generator`
   - 点击 "部署站点"

### 方法三：使用Wrangler CLI

1. **安装Wrangler**
   ```bash
   npm install -g wrangler
   ```

2. **登录Cloudflare**
   ```bash
   wrangler login
   ```

3. **部署项目**
   ```bash
   wrangler pages publish . --project-name xinmail-generator
   ```

## 环境变量配置

如果需要配置环境变量（如API Token），可以在Cloudflare Pages项目设置中添加：

1. 进入项目设置 → "环境变量"
2. 添加变量：
   - `API_TOKEN`: `apiceshi******`
   - `API_DOMAIN`: `ai999.dpdns.org`

## DNS配置

为了使用自定义域名 `eoceshi.552500.xyz`，需要在DNS提供商处添加以下记录：

```
类型: CNAME
名称: eoceshi
值: xinmail-generator.pages.dev
TTL: Auto
```

或者如果使用Cloudflare作为DNS提供商：

```
类型: CNAME
名称: eoceshi
值: xinmail-generator.pages.dev
代理状态: 已代理（橙色云朵）
```

## 验证部署

部署完成后，访问以下URL验证：

1. **Cloudflare Pages默认域名**: `https://xinmail-generator.pages.dev`
2. **自定义域名**: `https://eoceshi.552500.xyz`

## 常见问题

### 1. API调用失败
- 检查API域名是否正确：`ai999.dpdns.org`
- 确认Token是否有效：`apiceshi******`
- 检查CORS设置

### 2. 自定义域名无法访问
- 确认DNS记录已正确配置
- 等待DNS传播（可能需要几分钟到几小时）
- 检查SSL证书状态

### 3. 页面样式异常
- 确认所有CSS和JS文件都已正确上传
- 检查文件路径是否正确
- 清除浏览器缓存

## 更新部署

### Git仓库方式
```bash
git add .
git commit -m "更新内容"
git push origin main
```
Cloudflare Pages会自动检测到更改并重新部署。

### 直接上传方式
重新上传更新的文件到Cloudflare Pages。

### Wrangler CLI方式
```bash
wrangler pages publish . --project-name xinmail-generator
```

## 性能优化

项目已包含以下优化配置：

1. **缓存策略** (`_headers`文件)
   - CSS/JS文件：1年缓存
   - HTML文件：1小时缓存

2. **安全头部**
   - X-Frame-Options: DENY
   - X-Content-Type-Options: nosniff
   - X-XSS-Protection: 1; mode=block

3. **响应式设计**
   - 移动端优化
   - 现代浏览器支持

## 监控和分析

可以在Cloudflare Dashboard中查看：
- 访问统计
- 性能指标
- 错误日志
- 带宽使用情况

## 支持

如遇到部署问题，请检查：
1. Cloudflare Pages文档
2. 项目README.md文件
3. GitHub Issues（如果使用Git仓库）
