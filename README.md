# 邮箱生成器

一个基于Web的邮箱生成工具，可以快速生成临时邮箱地址并通过API自动创建账户。

## 功能特性

- 🎯 **智能前缀生成**: 支持自定义或自动生成邮箱前缀（5位数字+3位小写字母）
- 🚀 **批量生成**: 一次可生成1-10个邮箱地址
- 📋 **一键复制**: 支持单个或批量复制邮箱信息
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🔒 **安全可靠**: 使用HTTPS和安全的API调用

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: 现代化响应式设计，支持深色主题
- **部署**: Cloudflare Pages
- **API**: RESTful API集成

## 部署到Cloudflare Pages

### 方法一：通过Git仓库部署

1. 将代码推送到GitHub仓库
2. 登录Cloudflare Dashboard
3. 进入Pages页面，点击"创建项目"
4. 连接GitHub仓库
5. 配置构建设置：
   - 构建命令：留空
   - 构建输出目录：`/`
6. 点击"保存并部署"

### 方法二：直接上传文件

1. 登录Cloudflare Dashboard
2. 进入Pages页面，点击"上传资产"
3. 上传项目文件夹
4. 设置项目名称
5. 点击"部署站点"

### 自定义域名配置

1. 在Cloudflare Pages项目中，进入"自定义域"
2. 添加域名：`eoceshi.552500.xyz`
3. 按照提示配置DNS记录

## API配置

项目使用以下API配置：

```javascript
const API_CONFIG = {
    baseUrl: 'https://eoceshi.552500.xyz',
    token: 'apiceshi******',
    defaultPassword: 'Ai12345@',
    domain: '@ai999.dpdns.org'
};
```

### API接口说明

**添加用户接口**
- 地址：`POST /api/public/addUser`
- 请求头：`Authorization: apiceshi******`
- 请求体：
```json
{
  "list": [
    {
      "email": "<EMAIL>",
      "password": "Ai12345@",
      "roleName": ""
    }
  ]
}
```

## 使用说明

1. **设置邮箱前缀**（可选）
   - 留空：自动生成随机前缀
   - 自定义：输入8位字符（5位数字+3位小写字母）

2. **选择生成数量**
   - 范围：1-10个邮箱

3. **点击生成**
   - 系统将调用API创建邮箱账户
   - 显示生成结果和账户信息

4. **复制信息**
   - 单个复制：点击每个邮箱旁的复制按钮
   - 批量复制：点击"复制所有"按钮

## 文件结构

```
xinmail/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
├── _headers            # Cloudflare Pages头部配置
├── wrangler.toml       # Cloudflare Workers配置
└── README.md           # 项目说明
```

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 安全说明

- 所有API调用使用HTTPS加密
- 敏感信息不会存储在本地
- 支持现代浏览器的安全特性

## 许可证

MIT License - 详见LICENSE文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目域名：https://eoceshi.552500.xyz
- API域名：ai999.dpdns.org
