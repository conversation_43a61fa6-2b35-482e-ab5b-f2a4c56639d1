<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 邮箱生成器</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="error-page">
            <div class="error-content">
                <i class="fas fa-exclamation-triangle error-icon"></i>
                <h1>404</h1>
                <h2>页面未找到</h2>
                <p>抱歉，您访问的页面不存在。</p>
                <a href="/" class="back-home-btn">
                    <i class="fas fa-home"></i> 返回首页
                </a>
            </div>
        </div>
    </div>

    <style>
        .error-page {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }

        .error-content {
            background: white;
            padding: 60px 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
        }

        .error-icon {
            font-size: 4rem;
            color: #f56565;
            margin-bottom: 20px;
        }

        .error-content h1 {
            font-size: 6rem;
            color: #4a5568;
            margin: 0;
            font-weight: bold;
        }

        .error-content h2 {
            font-size: 2rem;
            color: #4a5568;
            margin: 20px 0;
        }

        .error-content p {
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .back-home-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .back-home-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .back-home-btn i {
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .error-content {
                padding: 40px 20px;
                margin: 20px;
            }
            
            .error-content h1 {
                font-size: 4rem;
            }
            
            .error-content h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</body>
</html>
