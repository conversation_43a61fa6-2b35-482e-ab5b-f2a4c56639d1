// API配置
const API_CONFIG = {
    baseUrl: 'https://ai999.dpdns.org',
    token: 'apiceshi',
    defaultPassword: 'Ai12345@',
    domain: '@ai999.dpdns.org'
};

// DOM元素
const elements = {
    generateBtn: document.getElementById('generateBtn'),
    emailPrefix: document.getElementById('emailPrefix'),
    emailCount: document.getElementById('emailCount'),
    resultsSection: document.getElementById('resultsSection'),
    emailResults: document.getElementById('emailResults'),
    copyAllBtn: document.getElementById('copyAllBtn'),
    loadingIndicator: document.getElementById('loadingIndicator'),
    statusMessage: document.getElementById('statusMessage')
};

// 生成随机前缀 (5位数字 + 3位小写字母)
function generateRandomPrefix() {
    const numbers = Math.floor(Math.random() * 90000) + 10000; // 10000-99999
    const letters = Array.from({length: 3}, () => 
        String.fromCharCode(97 + Math.floor(Math.random() * 26))
    ).join('');
    return numbers.toString() + letters;
}

// 验证前缀格式
function validatePrefix(prefix) {
    if (!prefix) return true; // 空前缀允许，将自动生成
    
    const regex = /^\d{5}[a-z]{3}$/;
    return regex.test(prefix);
}

// 显示状态消息
function showStatus(message, type = 'info') {
    elements.statusMessage.textContent = message;
    elements.statusMessage.className = `status-message ${type}`;
    elements.statusMessage.style.display = 'block';
    
    if (type === 'success' || type === 'error') {
        setTimeout(() => {
            elements.statusMessage.style.display = 'none';
        }, 5000);
    }
}

// 显示/隐藏加载指示器
function toggleLoading(show) {
    elements.loadingIndicator.style.display = show ? 'block' : 'none';
    elements.generateBtn.disabled = show;
}

// 调用API添加用户
async function addUsers(emailList) {
    try {
        const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': API_CONFIG.token
            },
            body: JSON.stringify({
                list: emailList
            })
        });

        const data = await response.json();
        
        if (response.ok && data.code === 200) {
            return { success: true, data };
        } else {
            throw new Error(data.message || `HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('API调用失败:', error);
        return { success: false, error: error.message };
    }
}

// 复制文本到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        return success;
    }
}

// 创建邮箱结果项
function createEmailItem(email, password) {
    const item = document.createElement('div');
    item.className = 'email-item';
    
    item.innerHTML = `
        <div class="email-info">
            <div class="email-address">${email}</div>
            <div class="email-password">密码: ${password}</div>
        </div>
        <button class="copy-btn" onclick="copyEmailInfo('${email}', '${password}')">
            <i class="fas fa-copy"></i> 复制
        </button>
    `;
    
    return item;
}

// 复制单个邮箱信息
async function copyEmailInfo(email, password) {
    const text = `邮箱: ${email}\n密码: ${password}`;
    const success = await copyToClipboard(text);
    
    if (success) {
        showStatus('邮箱信息已复制到剪贴板', 'success');
    } else {
        showStatus('复制失败，请手动复制', 'error');
    }
}

// 复制所有邮箱信息
async function copyAllEmails() {
    const emailItems = document.querySelectorAll('.email-item');
    let allText = '';
    
    emailItems.forEach((item, index) => {
        const email = item.querySelector('.email-address').textContent;
        const password = item.querySelector('.email-password').textContent.replace('密码: ', '');
        allText += `${index + 1}. 邮箱: ${email}\n   密码: ${password}\n\n`;
    });
    
    const success = await copyToClipboard(allText.trim());
    
    if (success) {
        showStatus('所有邮箱信息已复制到剪贴板', 'success');
    } else {
        showStatus('复制失败，请手动复制', 'error');
    }
}

// 生成邮箱
async function generateEmails() {
    const prefix = elements.emailPrefix.value.trim();
    const count = parseInt(elements.emailCount.value);
    
    // 验证输入
    if (!validatePrefix(prefix)) {
        showStatus('邮箱前缀格式错误！请输入5位数字+3位小写字母，如：12345abc', 'error');
        return;
    }
    
    if (count < 1 || count > 10) {
        showStatus('生成数量必须在1-10之间', 'error');
        return;
    }
    
    toggleLoading(true);
    showStatus('正在生成邮箱，请稍候...');
    
    try {
        const emailList = [];
        const usedPrefixes = new Set();
        
        for (let i = 0; i < count; i++) {
            let currentPrefix = prefix;
            
            // 如果没有提供前缀或者需要避免重复，生成新前缀
            if (!currentPrefix || usedPrefixes.has(currentPrefix)) {
                do {
                    currentPrefix = generateRandomPrefix();
                } while (usedPrefixes.has(currentPrefix));
            }
            
            usedPrefixes.add(currentPrefix);
            
            const email = currentPrefix + API_CONFIG.domain;
            emailList.push({
                email: email,
                password: API_CONFIG.defaultPassword,
                roleName: '' // 使用默认权限身份
            });
        }
        
        // 调用API
        const result = await addUsers(emailList);
        
        if (result.success) {
            // 显示结果
            elements.emailResults.innerHTML = '';
            emailList.forEach(item => {
                const emailItem = createEmailItem(item.email, item.password);
                elements.emailResults.appendChild(emailItem);
            });
            
            elements.resultsSection.style.display = 'block';
            showStatus(`成功生成 ${emailList.length} 个邮箱地址`, 'success');
            
            // 滚动到结果区域
            elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            showStatus(`生成失败: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('生成邮箱时发生错误:', error);
        showStatus('生成邮箱时发生未知错误，请稍后重试', 'error');
    } finally {
        toggleLoading(false);
    }
}

// 事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 生成按钮点击事件
    elements.generateBtn.addEventListener('click', generateEmails);
    
    // 复制所有按钮点击事件
    elements.copyAllBtn.addEventListener('click', copyAllEmails);
    
    // 前缀输入验证
    elements.emailPrefix.addEventListener('input', function() {
        const prefix = this.value.trim();
        if (prefix && !validatePrefix(prefix)) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#e2e8f0';
        }
    });
    
    // 回车键生成
    elements.emailPrefix.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            generateEmails();
        }
    });
    
    elements.emailCount.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            generateEmails();
        }
    });
});

// 全局函数，供HTML调用
window.copyEmailInfo = copyEmailInfo;
