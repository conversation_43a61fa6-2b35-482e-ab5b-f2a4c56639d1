/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header h1 i {
    margin-right: 15px;
    color: #ffd700;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.generator-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

/* 输入区域 */
.input-section h2 {
    color: #4a5568;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #4a5568;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.help-text {
    display: block;
    margin-top: 5px;
    color: #718096;
    font-size: 0.9rem;
}

/* 按钮样式 */
.generate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.generate-btn:active {
    transform: translateY(0);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 结果区域 */
.results-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #e2e8f0;
}

.results-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.email-results {
    margin-bottom: 20px;
}

.email-item {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.email-info {
    flex: 1;
}

.email-address {
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
}

.email-password {
    color: #718096;
    font-size: 0.9rem;
    margin-top: 5px;
}

.copy-btn {
    background: #48bb78;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.copy-btn:hover {
    background: #38a169;
}

.copy-all-btn {
    width: 100%;
    padding: 12px;
    background: #48bb78;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.copy-all-btn:hover {
    background: #38a169;
}

/* 状态区域 */
.status-section {
    text-align: center;
    margin: 20px 0;
}

.loading {
    color: white;
    font-size: 1.1rem;
}

.loading i {
    margin-right: 10px;
}

.status-message {
    padding: 10px;
    border-radius: 8px;
    margin: 10px 0;
    font-weight: 500;
}

.status-message.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.status-message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

.api-info {
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .generator-card {
        padding: 20px;
    }
    
    .email-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .copy-btn {
        align-self: flex-end;
    }
}
